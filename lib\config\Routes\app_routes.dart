import 'package:flutter/material.dart';
import '../../Core/models/portfolio_data_model.dart';
import '../../Core/Storage/Local/UserDataService/user_data_service.dart';
import '../../Features/Admin/Main_Dashboard/Presentation/Page/dashboard_page.dart';
import '../../Features/Auth/Presentation/Pages/login_page.dart';
import '../../Features/Auth/Presentation/Pages/register_page.dart';
import '../../Features/Portfolio/portfolio_main.dart';

/// Simple Routes System - Clean and Direct
class AppRoutes {
  // Static route constants
  static const String splash = '/';
  static const String login = '/login';
  static const String register = '/register';
  static const String dashboard = '/dashboard';
  static const String portfolio = '/portfolio';

  /// Build dashboard route with user email
  static String dashboardWithUser(String userEmail) {
    final username = userEmail.split('@').first;
    return '/dashboard/$username';
  }

  /// Build portfolio route with user email
  static String portfolioWithUser(String userEmail) {
    final username = userEmail.split('@').first;
    return '/portfolio/$username';
  }

  /// Build portfolio route with username directly
  static String portfolioByUsername(String username) {
    return '/portfolio/$username';
  }

  /// Extract username from route
  static String? extractUsernameFromRoute(String route) {
    if (route.startsWith('/dashboard/') || route.startsWith('/portfolio/')) {
      final parts = route.split('/');
      if (parts.length >= 3) return parts[2];
    }
    // Handle direct username routes (like /felopaters37)
    if (route.startsWith('/') && route.length > 1) {
      final parts = route.split('/');
      if (parts.length == 2 && parts[1].isNotEmpty) {
        final username = parts[1];
        // Check if it's not a static route
        if (![
          'login',
          'register',
          'dashboard',
          'portfolio',
        ].contains(username.toLowerCase())) {
          return username;
        }
      }
    }
    return null;
  }

  /// Check if user is logged in
  static bool isUserLoggedIn() {
    final userData = UserDataService.getUserData();
    return userData != null && userData['emailUser'] != null;
  }

  /// Get current user email
  static String? getCurrentUserEmail() {
    final userData = UserDataService.getUserData();
    return userData?['emailUser'] as String?;
  }

  /// Check if username belongs to current user
  static bool isCurrentUser(String username) {
    final currentEmail = getCurrentUserEmail();
    if (currentEmail == null) return false;
    final currentUsername = currentEmail.split('@').first;
    return username.toLowerCase() == currentUsername.toLowerCase();
  }

  /// Get initial route based on authentication
  static String getInitialRoute() {
    if (isUserLoggedIn()) {
      final userEmail = getCurrentUserEmail()!;
      return dashboardWithUser(userEmail);
    }
    return login; // Start with login if not logged in
  }
}

/// Route Generator - Optimized for Performance
class RouteGenerator {
  // Cache for frequently used routes
  static final Map<String, Widget Function()> _routeCache = {
    AppRoutes.login: () => const LoginPage(),
    AppRoutes.register: () => const RegisterPage(),
    AppRoutes.dashboard: () => const AdminDashboard(),
  };

  static Route<dynamic> generateRoute(RouteSettings settings) {
    final routeName = settings.name ?? '';

    // Handle username routes first (most common case)
    final username = AppRoutes.extractUsernameFromRoute(routeName);
    if (username != null) {
      // If it's current user, go to dashboard; otherwise portfolio
      if (AppRoutes.isCurrentUser(username)) {
        return _buildRoute(const AdminDashboard());
      } else {
        // Check if user exists and show portfolio
        return _buildRoute(const PortfolioMain());
      }
    }

    // Handle cached static routes for better performance
    if (_routeCache.containsKey(routeName)) {
      return _buildRoute(_routeCache[routeName]!());
    }

    // Handle portfolio route with arguments
    if (routeName == AppRoutes.portfolio) {
      String? email;
      PortfolioDataModel? portfolioData;

      if (settings.arguments is String) {
        email = settings.arguments as String;
      } else if (settings.arguments is PortfolioDataModel) {
        portfolioData = settings.arguments as PortfolioDataModel;
        email = portfolioData.email;
      }

      return _buildRoute(
        PortfolioMain(email: email, portfolioData: portfolioData),
      );
    }

    // Default fallback
    return _buildRoute(const LoginPage());
  }

  static MaterialPageRoute<dynamic> _buildRoute(Widget page) {
    return MaterialPageRoute(builder: (_) => page);
  }
}
