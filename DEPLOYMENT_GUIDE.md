# 🚀 DevFolio Deployment Guide

## Overview

This guide explains how to deploy DevFolio to Vercel with proper routing support for Flutter Web applications.

## 🔧 Configuration Files

### 1. `vercel.json` - Main Configuration

The `vercel.json` file handles:
- **Build Process**: Flutter build commands with optimizations
- **URL Rewrites**: Client-side routing support
- **Static Assets**: Proper serving of JS, CSS, images
- **Security Headers**: XSS protection, content type options
- **Caching**: Optimized cache strategies

### 2. `package.json` - Build Scripts

Contains deployment scripts:
```bash
npm run build      # Production build
npm run deploy     # Deploy to Vercel
npm run preview    # Local preview
```

### 3. `web/_redirects` - Netlify Support

Backup configuration for Netlify deployment.

## 🌐 Deployment Steps

### Option 1: Vercel CLI

1. **Install Vercel CLI**:
```bash
npm install -g vercel
```

2. **Login to Vercel**:
```bash
vercel login
```

3. **Deploy**:
```bash
npm run deploy
```

### Option 2: GitHub Integration

1. **Push to GitHub**:
```bash
git add .
git commit -m "Deploy to Vercel"
git push origin main
```

2. **Connect to Vercel**:
   - Go to [vercel.com](https://vercel.com)
   - Import your GitHub repository
   - Vercel will automatically detect the configuration

### Option 3: Manual Upload

1. **Build locally**:
```bash
npm run build
```

2. **Upload `build/web` folder** to Vercel dashboard

## 🛠️ Routing Configuration

### URL Rewrite Rules

The `vercel.json` includes specific rules for:

#### Static Assets
- `/favicon.png` → Direct serving
- `/icons/*` → Icon files
- `/assets/*` → Flutter assets
- `/*.js`, `/*.css` → JavaScript and CSS files

#### Application Routes
- `/login` → `index.html`
- `/register` → `index.html`
- `/dashboard` → `index.html`
- `/dashboard/*` → `index.html`
- `/portfolio/*` → `index.html`
- `/username` → `index.html` (direct username access)

#### Catch-All
- `/*` → `index.html` (fallback for any unmatched routes)

## 🔒 Security Headers

Automatically applied headers:
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `Referrer-Policy: strict-origin-when-cross-origin`

## 📦 Caching Strategy

### Static Assets (1 year)
- JavaScript files
- CSS files
- Images and icons
- WASM files

### Dynamic Content
- `index.html`: 1 hour cache
- Service Worker: No cache (always fresh)

## 🧪 Testing Deployment

### 1. Local Testing
```bash
# Build and preview locally
npm run build
npm run preview

# Open http://localhost:8000
```

### 2. Test Routes
After deployment, test these URLs:
- `https://your-app.vercel.app/`
- `https://your-app.vercel.app/login`
- `https://your-app.vercel.app/register`
- `https://your-app.vercel.app/dashboard`
- `https://your-app.vercel.app/portfolio/username`
- `https://your-app.vercel.app/username`

### 3. Performance Testing
- Check loading speed
- Test Service Worker caching
- Verify route transitions

## 🐛 Troubleshooting

### 404 Errors on Direct URLs
**Problem**: Direct navigation to routes returns 404
**Solution**: Ensure `vercel.json` rewrite rules are correct

### Static Assets Not Loading
**Problem**: CSS/JS files return 404
**Solution**: Check asset paths in rewrite rules

### Slow Loading
**Problem**: App loads slowly
**Solution**: Verify caching headers and Service Worker

### Build Failures
**Problem**: Vercel build fails
**Solution**: Check Flutter version and dependencies

## 📊 Performance Optimizations

### Build Optimizations
- `--tree-shake-icons`: Removes unused icons
- `--web-renderer html`: Better compatibility
- `--release`: Production optimizations

### Runtime Optimizations
- Service Worker caching
- Static asset compression
- Optimized cache headers

## 🔄 Continuous Deployment

### Automatic Deployment
1. Connect GitHub repository to Vercel
2. Enable automatic deployments
3. Every push to `main` triggers deployment

### Environment Variables
Set in Vercel dashboard:
- `FLUTTER_WEB_USE_SKIA=false`
- `FLUTTER_WEB_AUTO_DETECT=false`

## 📝 Notes

- **Flutter Version**: Ensure compatible Flutter version
- **Dependencies**: All dependencies must be web-compatible
- **Routes**: Test all routes after deployment
- **Performance**: Monitor Core Web Vitals
- **Security**: Verify HTTPS and security headers

---

**Last Updated**: 2025-01-23
**Version**: 1.0.0
**Author**: DevFolio Team
