# Flutter Web SPA Redirects for Netlify
# Static assets should be served directly
/favicon.png    /favicon.png    200
/manifest.json  /manifest.json  200
/sw.js          /sw.js          200
/icons/*        /icons/:splat   200
/assets/*       /assets/:splat  200
/canvaskit/*    /canvaskit/:splat 200
/*.js           /:splat.js      200
/*.css          /:splat.css     200
/*.wasm         /:splat.wasm    200
/*.map          /:splat.map     200

# Application routes - redirect to index.html for client-side routing
/login          /index.html     200
/register       /index.html     200
/dashboard      /index.html     200
/dashboard/*    /index.html     200
/portfolio      /index.html     200
/portfolio/*    /index.html     200

# Catch-all for any other routes (including direct username access)
/*              /index.html     200
