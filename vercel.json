{"rewrites": [{"source": "/login", "destination": "/index.html"}, {"source": "/register", "destination": "/index.html"}, {"source": "/dashboard", "destination": "/index.html"}, {"source": "/dashboard/(.*)", "destination": "/index.html"}, {"source": "/portfolio", "destination": "/index.html"}, {"source": "/portfolio/(.*)", "destination": "/index.html"}, {"source": "/([^./]+)", "destination": "/index.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}, {"source": "/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}