{"buildCommand": "flutter build web --release --web-renderer html --base-href / --tree-shake-icons", "outputDirectory": "build/web", "installCommand": "if cd flutter; then git pull && cd ..; else git clone https://github.com/flutter/flutter.git; fi && flutter/bin/flutter doctor && flutter/bin/flutter clean && flutter/bin/flutter pub get", "framework": null, "rewrites": [{"source": "/login", "destination": "/index.html"}, {"source": "/register", "destination": "/index.html"}, {"source": "/dashboard", "destination": "/index.html"}, {"source": "/dashboard/(.*)", "destination": "/index.html"}, {"source": "/portfolio", "destination": "/index.html"}, {"source": "/portfolio/(.*)", "destination": "/index.html"}, {"source": "/([^./]+)", "destination": "/index.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}, {"source": "/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}