{"buildCommand": "flutter build web --release --web-renderer html --base-href / --tree-shake-icons", "outputDirectory": "build/web", "installCommand": "if cd flutter; then git pull && cd ..; else git clone https://github.com/flutter/flutter.git; fi && flutter/bin/flutter doctor && flutter/bin/flutter clean && flutter/bin/flutter pub get", "framework": null, "rewrites": [{"source": "/favicon.png", "destination": "/favicon.png"}, {"source": "/manifest.json", "destination": "/manifest.json"}, {"source": "/sw.js", "destination": "/sw.js"}, {"source": "/icons/(.*)", "destination": "/icons/$1"}, {"source": "/assets/(.*)", "destination": "/assets/$1"}, {"source": "/canvaskit/(.*)", "destination": "/canvaskit/$1"}, {"source": "/(.*\\.js)", "destination": "/$1"}, {"source": "/(.*\\.css)", "destination": "/$1"}, {"source": "/(.*\\.wasm)", "destination": "/$1"}, {"source": "/(.*\\.map)", "destination": "/$1"}, {"source": "/(.*\\.json)", "destination": "/$1"}, {"source": "/flutter_bootstrap.js", "destination": "/flutter_bootstrap.js"}, {"source": "/main.dart.js", "destination": "/main.dart.js"}, {"source": "/flutter_service_worker.js", "destination": "/flutter_service_worker.js"}, {"source": "/login", "destination": "/index.html"}, {"source": "/register", "destination": "/index.html"}, {"source": "/dashboard", "destination": "/index.html"}, {"source": "/dashboard/(.*)", "destination": "/index.html"}, {"source": "/portfolio", "destination": "/index.html"}, {"source": "/portfolio/(.*)", "destination": "/index.html"}, {"source": "/([^/]+)", "destination": "/index.html"}, {"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}, {"source": "/(.*\\.(js|css|wasm|png|jpg|jpeg|gif|svg|ico|json))", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/index.html", "headers": [{"key": "Cache-Control", "value": "public, max-age=3600"}]}, {"source": "/sw.js", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}], "functions": {"build/web/index.html": {"includeFiles": "build/web/**"}}}