import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:web/web.dart' as web;
import 'app_routes.dart';

/// Simple Navigation Service - Essential Functions Only
class AppNavigationService {
  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  NavigatorState? get navigator => navigatorKey.currentState;

  /// Basic navigation to route
  Future<void> navigateTo(String route, {Object? arguments}) async {
    try {
      await navigator?.pushNamed(route, arguments: arguments);
      _updateBrowserUrl(route);
    } catch (e) {
      log('Navigation error: $e');
    }
  }

  /// Replace current route
  Future<void> replaceWith(String route, {Object? arguments}) async {
    try {
      await navigator?.pushReplacementNamed(route, arguments: arguments);
      _updateBrowserUrl(route);
    } catch (e) {
      log('Navigation error: $e');
    }
  }

  /// Clear stack and navigate
  Future<void> clearAndNavigateTo(String route, {Object? arguments}) async {
    try {
      await navigator?.pushNamedAndRemoveUntil(
        route,
        (route) => false,
        arguments: arguments,
      );
      _updateBrowserUrl(route);
    } catch (e) {
      log('Navigation error: $e');
    }
  }

  /// Go back
  void goBack() {
    try {
      navigator?.pop();
    } catch (e) {
      log('Navigation error: $e');
    }
  }

  /// Navigate to dashboard with current user
  Future<void> navigateToDashboard() async {
    final userEmail = AppRoutes.getCurrentUserEmail();
    if (userEmail != null) {
      final route = AppRoutes.dashboardWithUser(userEmail);
      await clearAndNavigateTo(route);
    } else {
      await clearAndNavigateTo(AppRoutes.login);
    }
  }

  /// Navigate to dashboard with current user (alias for compatibility)
  Future<void> navigateToDashboardWithUser() async {
    await navigateToDashboard();
  }

  /// Navigate to portfolio with current user
  Future<void> navigateToPortfolio() async {
    final userEmail = AppRoutes.getCurrentUserEmail();
    if (userEmail != null) {
      final route = AppRoutes.portfolioWithUser(userEmail);
      await navigateTo(route);
    } else {
      await navigateTo(AppRoutes.portfolio);
    }
  }

  /// Navigate to portfolio with current user (alias for compatibility)
  Future<void> navigateToPortfolioWithUser() async {
    await navigateToPortfolio();
  }

  /// Navigate to portfolio by username
  Future<void> navigateToPortfolioByUsername(String username) async {
    final route = AppRoutes.portfolioByUsername(username);
    await navigateTo(route);
  }

  /// Update browser URL (web only)
  void _updateBrowserUrl(String route) {
    if (kIsWeb) {
      try {
        final cleanRoute = route.startsWith('/') ? route : '/$route';
        final currentUrl = web.window.location.href;
        final uri = Uri.parse(currentUrl);
        final newUri = uri.replace(path: cleanRoute);
        web.window.history.replaceState(null, '', newUri.toString());
      } catch (e) {
        log('Error updating browser URL: $e');
      }
    }
  }

  /// Public method to update browser URL (for compatibility)
  void updateBrowserUrl(String route) {
    _updateBrowserUrl(route);
  }
}
