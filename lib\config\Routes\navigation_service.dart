import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:web/web.dart' as web;
import 'app_routes.dart';

/// Simple Navigation Service - Essential Functions Only
class AppNavigationService {
  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  NavigatorState? get navigator => navigatorKey.currentState;

  /// Basic navigation to route - Optimized
  Future<void> navigateTo(String route, {Object? arguments}) async {
    if (navigator == null) return;

    try {
      // Update URL immediately for better UX
      _updateBrowserUrl(route);
      await navigator!.pushNamed(route, arguments: arguments);
    } catch (e) {
      log('Navigation error: $e');
    }
  }

  /// Replace current route - Optimized
  Future<void> replaceWith(String route, {Object? arguments}) async {
    if (navigator == null) return;

    try {
      // Update URL immediately for better UX
      _updateBrowserUrl(route);
      await navigator!.pushReplacementNamed(route, arguments: arguments);
    } catch (e) {
      log('Navigation error: $e');
    }
  }

  /// Clear stack and navigate - Optimized
  Future<void> clearAndNavigateTo(String route, {Object? arguments}) async {
    if (navigator == null) return;

    try {
      // Update URL immediately for better UX
      _updateBrowserUrl(route);
      await navigator!.pushNamedAndRemoveUntil(
        route,
        (route) => false,
        arguments: arguments,
      );
    } catch (e) {
      log('Navigation error: $e');
    }
  }

  /// Go back
  void goBack() {
    try {
      navigator?.pop();
    } catch (e) {
      log('Navigation error: $e');
    }
  }

  /// Navigate to dashboard with current user
  Future<void> navigateToDashboard() async {
    final userEmail = AppRoutes.getCurrentUserEmail();
    if (userEmail != null) {
      final route = AppRoutes.dashboardWithUser(userEmail);
      await clearAndNavigateTo(route);
    } else {
      await clearAndNavigateTo(AppRoutes.login);
    }
  }

  /// Navigate to dashboard with current user (alias for compatibility)
  Future<void> navigateToDashboardWithUser() async {
    await navigateToDashboard();
  }

  /// Navigate to portfolio with current user
  Future<void> navigateToPortfolio() async {
    final userEmail = AppRoutes.getCurrentUserEmail();
    if (userEmail != null) {
      final route = AppRoutes.portfolioWithUser(userEmail);
      await navigateTo(route);
    } else {
      await navigateTo(AppRoutes.portfolio);
    }
  }

  /// Navigate to portfolio with current user (alias for compatibility)
  Future<void> navigateToPortfolioWithUser() async {
    await navigateToPortfolio();
  }

  /// Navigate to portfolio by username
  Future<void> navigateToPortfolioByUsername(String username) async {
    final route = AppRoutes.portfolioByUsername(username);
    await navigateTo(route);
  }

  /// Update browser URL (web only) - Optimized
  void _updateBrowserUrl(String route) {
    if (kIsWeb) {
      try {
        final cleanRoute = route.startsWith('/') ? route : '/$route';

        // Only update if URL is different to avoid unnecessary operations
        if (web.window.location.pathname != cleanRoute) {
          web.window.history.replaceState(null, '', cleanRoute);
        }
      } catch (e) {
        log('Error updating browser URL: $e');
      }
    }
  }

  /// Public method to update browser URL (for compatibility)
  void updateBrowserUrl(String route) {
    _updateBrowserUrl(route);
  }
}
