#!/bin/bash

# Flutter build script for Netlify
set -e

echo "🚀 Starting Flutter Web build for Netlify..."

# Check if Flutter is already installed
if ! command -v flutter &> /dev/null; then
    echo "📦 Installing Flutter..."
    
    # Download and install Flutter
    cd /opt/buildhome
    wget -q https://storage.googleapis.com/flutter_infra_release/releases/stable/linux/flutter_linux_3.24.5-stable.tar.xz
    tar xf flutter_linux_3.24.5-stable.tar.xz
    
    # Add Flutter to PATH
    export PATH="$PATH:/opt/buildhome/flutter/bin"
    
    echo "✅ Flutter installed successfully"
else
    echo "✅ Flutter already available"
fi

# Navigate back to project directory
cd $NETLIFY_BUILD_BASE

# Configure Flutter
echo "⚙️ Configuring Flutter..."
flutter config --enable-web
flutter doctor

# Get dependencies
echo "📚 Getting Flutter dependencies..."
flutter pub get

# Build web app
echo "🔨 Building Flutter web app..."
flutter build web --release --web-renderer html --base-href /

# Copy additional files
echo "📋 Copying additional files..."
if [ -f "web/_redirects" ]; then
    cp web/_redirects build/web/_redirects
fi

if [ -f "web/sw.js" ]; then
    cp web/sw.js build/web/sw.js
fi

if [ -f "web/.htaccess" ]; then
    cp web/.htaccess build/web/.htaccess
fi

echo "🎉 Build completed successfully!"
echo "📁 Build output is in: build/web"

# List build directory contents for debugging
echo "📋 Build directory contents:"
ls -la build/web/
