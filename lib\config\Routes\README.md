# 🚀 Simple Routes System

## 📁 Structure
```
Routes/
├── app_routes.dart         # Routes and route generation
├── navigation_service.dart # Navigation functions
├── index.dart             # Single import point
└── README.md              # This file
```

## 📦 Import
```dart
import 'package:devfolio/config/Routes/index.dart';
```

## 🎯 Core Functions

### Routes
```dart
// Static routes
AppRoutes.login           // '/login'
AppRoutes.register        // '/register'
AppRoutes.dashboard       // '/dashboard'
AppRoutes.portfolio       // '/portfolio'

// Dynamic routes (email-based)
AppRoutes.dashboardWithUser(email)     // '/dashboard/username'
AppRoutes.portfolioWithUser(email)     // '/portfolio/username'
AppRoutes.portfolioByUsername(username) // '/portfolio/username'

// Utilities
AppRoutes.extractUsernameFromRoute(route)  // Extract username from URL
AppRoutes.isUserLoggedIn()                 // Check if user is logged in
AppRoutes.getCurrentUserEmail()            // Get current user email
AppRoutes.isCurrentUser(username)          // Check if username is current user
AppRoutes.getInitialRoute()               // Get initial route based on auth
```

### Navigation
```dart
// Basic navigation
kNavigationService.navigateTo(route)
kNavigationService.replaceWith(route)
kNavigationService.clearAndNavigateTo(route)
kNavigationService.goBack()

// Quick navigation
kNavigationService.navigateToDashboard()
kNavigationService.navigateToPortfolio()
kNavigationService.navigateToPortfolioByUsername(username)

// Compatibility aliases
kNavigationService.navigateToDashboardWithUser()
kNavigationService.navigateToPortfolioWithUser()
```

## 🔐 Authentication Flow

### If User NOT Logged In:
- Start with **Login page**
- Direct username URLs (like `/john`) → Show portfolio

### If User IS Logged In:
- Start with **Dashboard** (with user's email in URL)
- Own username URL → Dashboard
- Other username URL → Portfolio

## 🌐 URL Examples

| URL | User Status | Result |
|-----|-------------|--------|
| `/` | Not logged in | Login page |
| `/` | Logged in | Dashboard with user email |
| `/john` | Not logged in | John's portfolio |
| `/john` | John logged in | John's dashboard |
| `/john` | Mary logged in | John's portfolio |
| `/login` | Any | Login page |
| `/dashboard/john` | John logged in | John's dashboard |
| `/portfolio/john` | Any | John's portfolio |

## ✨ Key Features

- **Simple**: Only 3 files, clear structure
- **Email-based**: Uses email for route building (username = email.split('@')[0])
- **Authentication-aware**: Different behavior based on login status
- **Direct URLs**: Support for direct username access
- **Web-friendly**: Automatic browser URL updates
- **Clean**: No duplicate functions, minimal complexity

## 🎯 Usage Examples

### Navigate to Dashboard
```dart
kNavigationService.navigateToDashboard();
```

### Navigate to Portfolio
```dart
kNavigationService.navigateToPortfolioByUsername('john');
```

### Check Authentication
```dart
if (AppRoutes.isUserLoggedIn()) {
  // User is logged in
  final email = AppRoutes.getCurrentUserEmail();
}
```

### Build Routes
```dart
final dashboardRoute = AppRoutes.dashboardWithUser('<EMAIL>');
// Result: '/dashboard/john'
```
