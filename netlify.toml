[build]
  publish = "build/web"
  command = """
    # Install Flutter
    cd /opt/buildhome
    wget -q https://storage.googleapis.com/flutter_infra_release/releases/stable/linux/flutter_linux_3.24.5-stable.tar.xz
    tar xf flutter_linux_3.24.5-stable.tar.xz
    export PATH="$PATH:/opt/buildhome/flutter/bin"
    cd $NETLIFY_BUILD_BASE

    # Build Flutter app
    flutter config --enable-web
    flutter pub get
    flutter build web --release --web-renderer html --base-href /

    # Copy additional files
    cp web/_redirects build/web/_redirects || true
    cp web/sw.js build/web/sw.js || true
    cp web/.htaccess build/web/.htaccess || true
  """

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[build.environment]
  NODE_VERSION = "18"
