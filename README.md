# DevFolio - Professional Portfolio

A Flutter web application for creating and showcasing professional portfolios.

## Features

- 🎨 Modern, responsive design
- 👤 User authentication and registration
- 📊 Admin dashboard for portfolio management
- 🌐 Public portfolio pages with custom URLs
- 📱 Mobile-friendly interface
- 🔗 Direct username-based URLs (e.g., `/username`)

## URL Structure

- `/` - Landing page
- `/login` - User login
- `/register` - User registration
- `/dashboard` - Admin dashboard (requires authentication)
- `/dashboard/username` - User-specific dashboard
- `/portfolio/username` - Public portfolio view
- `/username` - Direct access to user's portfolio

## Deployment

This project is optimized for deployment on Vercel with automatic builds and proper routing support.

### Vercel Configuration

The project includes comprehensive deployment configuration:

- `vercel.json` - Complete Vercel deployment configuration with:
  - Flutter build commands
  - URL rewriting rules for client-side routing
  - Static asset handling
  - Security headers
  - Caching strategies
- `package.json` - Build scripts and dependencies
- `web/_redirects` - Netlify SPA routing support (backup)
- `web/.htaccess` - Apache server configuration
- `web/sw.js` - Service Worker for caching

### Build Commands

```bash
# Local development
npm run dev
# or
flutter run -d web-server --web-port 3000

# Production build
npm run build
# or
flutter build web --release --web-renderer html --base-href / --tree-shake-icons

# Deploy to Vercel
npm run deploy
# or
vercel --prod

# Preview build locally
npm run preview
```

### Routing Support

The deployment configuration ensures that direct URL navigation works correctly:

- `/login` - Login page
- `/register` - Registration page
- `/dashboard` - User dashboard
- `/dashboard/username` - User-specific dashboard
- `/portfolio/username` - Public portfolio view
- `/username` - Direct username access

All routes are properly handled by the client-side router with fallback to `index.html`.

## Development

1. Install Flutter SDK
2. Run `flutter pub get`
3. Start development server: `flutter run -d web-server`

## Web Routing

The application uses Flutter's path-based URL strategy for clean URLs without hash fragments. All routes are handled client-side with proper fallback to `index.html` for direct URL access.

## Database

Uses Supabase for backend services including:

- User authentication
- Portfolio data storage
- User verification by username