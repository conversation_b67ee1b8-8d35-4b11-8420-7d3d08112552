{"name": "de<PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "Professional Portfolio Flutter Web App", "scripts": {"build": "flutter build web --release --web-renderer html --base-href / --tree-shake-icons && cp web/_redirects build/web/_redirects && cp web/sw.js build/web/sw.js && cp web/.htaccess build/web/.htaccess", "dev": "flutter run -d web-server --web-port 3000", "clean": "flutter clean", "deploy": "vercel --prod", "vercel-build": "flutter build web --release --web-renderer html --base-href / --tree-shake-icons && cp web/_redirects build/web/_redirects && cp web/sw.js build/web/sw.js && cp web/.htaccess build/web/.htaccess", "analyze": "flutter analyze", "test": "flutter test", "preview": "cd build/web && python -m http.server 8000"}, "keywords": ["flutter", "portfolio", "web", "dart", "vercel", "responsive"], "author": "DevFolio Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/felopatersameh/devfolio.git"}, "devDependencies": {"vercel": "^32.0.0"}, "engines": {"node": ">=18.0.0"}}