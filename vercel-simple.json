{"buildCommand": "flutter build web --release --web-renderer html --base-href / --tree-shake-icons", "outputDirectory": "build/web", "installCommand": "if cd flutter; then git pull && cd ..; else git clone https://github.com/flutter/flutter.git; fi && flutter/bin/flutter doctor && flutter/bin/flutter clean && flutter/bin/flutter pub get", "framework": null, "rewrites": [{"source": "/((?!.*\\.).*)", "destination": "/index.html"}]}