@echo off
echo 🚀 Building Flutter Web App...
flutter build web --release --web-renderer html --base-href /

echo 📋 Copying additional files...
copy web\_redirects build\web\_redirects >nul 2>&1
copy web\sw.js build\web\sw.js >nul 2>&1
copy web\.htaccess build\web\.htaccess >nul 2>&1

echo ✅ Build complete!
echo.
echo 📁 Your app is ready in: build\web
echo.
echo 🚀 Quick deployment options:
echo 1. Drag 'build\web' folder to netlify.com (EASIEST!)
echo 2. Use Firebase: firebase deploy
echo 3. Use Surge: cd build\web && surge
echo 4. Use GitHub Pages (set folder to build\web)
echo.
echo 💡 Tip: For fastest deployment, just drag build\web to netlify.com!
echo.
pause
