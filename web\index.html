<!DOCTYPE html>
<html lang="en">
<head>
  <base href="/">

  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">

  <!-- Performance optimizations -->
  <meta http-equiv="Cache-Control" content="public, max-age=31536000">
  <meta name="theme-color" content="#6366F1">

  <!-- SEO Meta Tags -->
  <title>DevFolio - Professional Portfolio</title>
  <meta name="description" content="Professional Portfolio - Showcase your skills and projects">
  <meta name="keywords" content="portfolio, developer, flutter, web development">
  <meta name="author" content="DevFolio">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:title" content="DevFolio - Professional Portfolio">
  <meta property="og:description" content="Professional Portfolio - Showcase your skills and projects">
  <meta property="og:site_name" content="DevFolio">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:title" content="DevFolio - Professional Portfolio">
  <meta property="twitter:description" content="Professional Portfolio - Showcase your skills and projects">

  <!-- PWA Meta Tags -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta name="apple-mobile-web-app-title" content="DevFolio">

  <!-- Icons -->
  <link rel="icon" type="image/png" href="favicon.png">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">
  <link rel="manifest" href="manifest.json">

  <!-- Preload critical resources -->
  <link rel="preload" href="flutter_bootstrap.js" as="script">
  <link rel="dns-prefetch" href="//fonts.googleapis.com">

  <style>
    /* Critical CSS - Inline for performance */
    body {
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
      color: #ffffff;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      overflow-x: hidden;
    }

    .loading {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      z-index: 9999;
      transition: opacity 0.3s ease-out;
    }

    .loading.fade-out {
      opacity: 0;
      pointer-events: none;
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 2px solid rgba(99, 102, 241, 0.2);
      border-top: 2px solid #6366F1;
      border-radius: 50%;
      animation: spin 0.8s linear infinite;
      margin-bottom: 16px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .loading-text {
      color: #6366F1;
      font-size: 14px;
      font-weight: 500;
      letter-spacing: 0.5px;
    }

    /* Prevent flash of unstyled content */
    flutter-view {
      opacity: 0;
      transition: opacity 0.3s ease-in;
    }

    flutter-view.loaded {
      opacity: 1;
    }
  </style>
</head>
<body>
  <!-- Loading Screen -->
  <div id="loading" class="loading">
    <div class="spinner"></div>
    <div class="loading-text">Loading Portfolio...</div>
  </div>

  <script>
    // Performance optimized loading script
    (function() {
      'use strict';

      const loading = document.getElementById('loading');
      const flutterView = document.querySelector('flutter-view');
      let isAppReady = false;

      // Hide loading screen with smooth transition
      function hideLoading() {
        if (loading && !isAppReady) {
          isAppReady = true;
          loading.classList.add('fade-out');
          if (flutterView) {
            flutterView.classList.add('loaded');
          }
          setTimeout(() => {
            loading.style.display = 'none';
          }, 300);
        }
      }

      // Listen for Flutter ready event
      window.addEventListener('flutter-first-frame', hideLoading);

      // Fallback timeout (reduced from 10s to 5s)
      setTimeout(hideLoading, 5000);

      // Performance monitoring
      if ('performance' in window) {
        window.addEventListener('load', function() {
          setTimeout(function() {
            const perfData = performance.getEntriesByType('navigation')[0];
            if (perfData && perfData.loadEventEnd > 3000) {
              console.warn('Slow page load detected:', perfData.loadEventEnd + 'ms');
            }
          }, 0);
        });
      }

      // Register Service Worker for caching and performance
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
          navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
              console.log('SW registered: ', registration);
            })
            .catch(function(registrationError) {
              console.log('SW registration failed: ', registrationError);
            });
        });
      }
    })();
  </script>

  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
